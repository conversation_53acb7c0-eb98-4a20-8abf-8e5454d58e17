// Generic outbound agent that handles both CSV and API processing through handlers
const logger = require('../config/logger');
const PerformanceMonitor = require('../utils/performance');
const {
  setupQueueConsumer,
  getHandler,
  setupGracefulShutdown,
  validateAgentConfig,
  logAgentActivity
} = require('../utils/agentHelper');

/**
 * Processes outbound agent based on source type (CSV or API)
 * @param {Object} agent - Agent configuration
 * @returns {Promise<void>}
 */
async function processOutboundAgent(agent) {
  const performanceMonitor = new PerformanceMonitor(`Outbound Processing - ${agent.name}`);

  try {
    logAgentActivity(agent.name, 'Starting outbound processing', {
      type: agent.type,
      source: agent.source,
      handler: agent.handler
    });

    // Validate agent configuration
    if (!validateAgentConfig(agent)) {
      throw new Error(`Invalid agent configuration for ${agent.name}`);
    }

    // Get the appropriate handler
    const handlerFunction = getHandler(agent.handler);

    performanceMonitor.startStep('Handler Execution', {
      agentName: agent.name,
      handlerName: agent.handler,
      source: agent.source
    });

    // Execute handler based on source type
    let result;
    if (agent.source === 'API') {
      // For API agents, we need to set up queue listening
      await setupApiQueueListener(agent, handlerFunction, performanceMonitor);
      return; // API agents run continuously
    } else {
      // For CSV/file-based agents, execute once
      result = await handlerFunction({ agent, performanceMonitor });
    }

    performanceMonitor.endStep('Handler Execution', {
      success: !!result,
      resultType: typeof result
    });

    const finalMetrics = performanceMonitor.complete({
      status: 'success',
      agentName: agent.name,
      handlerName: agent.handler,
      result
    });

    logAgentActivity(agent.name, 'Outbound processing completed successfully', {
      performanceMetrics: finalMetrics.summary
    });

  } catch (error) {
    logger.error(`Error in outbound agent processing for ${agent.name}:`, error.message);

    performanceMonitor.complete({
      status: 'error',
      error: error.message,
      agentName: agent.name
    });

    throw error;
  }
}

/**
 * Sets up queue listener for API outbound agents
 * @param {Object} agent - Agent configuration
 * @param {Function} handlerFunction - Handler function to process messages
 * @param {Object} performanceMonitor - Performance monitor instance
 * @returns {Promise<void>}
 */
async function setupApiQueueListener(agent, handlerFunction, performanceMonitor) {
  try {
    logAgentActivity(agent.name, `Setting up queue listener for: ${agent.queue}`);

    // Message handler function
    const messageHandler = async (msg, channel) => {
      try {
        performanceMonitor.startStep('Message Processing', {
          queueName: agent.queue,
          agentName: agent.name,
          messageSize: msg.content.length
        });

        // Parse message
        const messageContent = msg.content.toString();
        let event;

        try {
          event = JSON.parse(messageContent);
        } catch (parseError) {
          logger.error(`[${agent.name}] Failed to parse message from queue ${agent.queue}:`, parseError);
          channel.ack(msg);
          return;
        }

        logAgentActivity(agent.name, `Processing event from queue ${agent.queue}`, {
          eventType: event.event_type,
          traceId: event.trace_id
        });

        // Process the event through handler
        const result = await handlerFunction({
          event,
          agent,
          performanceMonitor: performanceMonitor
        });

        const finalMetrics = performanceMonitor.complete({
          status: 'success',
          agentName: agent.name,
          result
        });

        logAgentActivity(agent.name, `Successfully processed event`, {
          performanceMetrics: finalMetrics.summary,
          result
        });

        // Acknowledge message
        channel.ack(msg);

      } catch (error) {
        logger.error(`[${agent.name}] Error processing message from queue ${agent.queue}:`, error);

        performanceMonitor.complete({
          status: 'error',
          error: error.message,
          agentName: agent.name
        });

        // Reject message and requeue
        channel.nack(msg, false, true);
      }
    };

    // Setup queue consumer
    await setupQueueConsumer(agent.queue, messageHandler);

    logAgentActivity(agent.name, `Queue listener established for ${agent.queue}`);

    // Setup graceful shutdown
    setupGracefulShutdown(async () => {
      logAgentActivity(agent.name, 'Shutting down API outbound agent');
    });

    // Keep process alive
    logAgentActivity(agent.name, `API outbound agent is now listening for events...`);

  } catch (error) {
    logger.error(`[${agent.name}] Failed to setup queue listener for ${agent.queue}:`, error);
    throw error;
  }
}

/**
 * Main outbound agent handler - routes to appropriate processing based on agent configuration
 * @param {Object} agent - Agent configuration
 * @returns {Promise<void>}
 */
async function outboundAgentHandler(agent) {
  try {
    logAgentActivity(agent.name, 'Starting outbound agent handler', {
      type: agent.type,
      source: agent.source,
      queue: agent.queue,
      handler: agent.handler
    });

    await processOutboundAgent(agent);

  } catch (error) {
    logger.error(`Outbound agent handler failed for ${agent.name}:`, error.message);
    throw error;
  }
}

module.exports = { outboundAgentHandler };
