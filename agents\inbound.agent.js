// Handles inbound agent processing (with cron, validation, etc.)
const logger = require('../config/logger');
const PerformanceMonitor = require('../utils/performance');
const { getCachedAgentSources, getCachedCronConfigs } = require('../utils/caching');
const { validateConfigs, processCSVFiles } = require('../services/csv.service');
const cron = require('node-cron');

async function inboundAgentHandler(job = null, specificAgent = null) {
    const performanceMonitor = new PerformanceMonitor('CSV Processing Job');
    try {
        let raw;
        if (specificAgent) {
            // Single agent mode
            performanceMonitor.startStep('Process Specific Agent', {
                jobName: job?.name || 'Manual Execution',
                agentName: specificAgent.name,
                agentQueue: specificAgent.queue
            });
            raw = [specificAgent];
            performanceMonitor.endStep('Process Specific Agent', { agentCount: 1 });

            // For inbound agents, also schedule cron jobs if not already scheduled
            await scheduleCronJobsForInbound();
        } else {
            // Multi-agent mode (fallback, though not used in current implementation)
            performanceMonitor.startStep('Get Agent Sources', { jobName: job?.name || 'Manual Execution' });
            raw = await getCachedAgentSources('inbound');
            performanceMonitor.endStep('Get Agent Sources', { agentCount: raw?.length || 0 });
        }

        performanceMonitor.startStep('Validate Configurations', { rawAgentCount: raw?.length || 0 });
        const valid = await validateConfigs(raw);
        performanceMonitor.endStep('Validate Configurations', {
            validAgentCount: valid.length,
            invalidAgentCount: (raw?.length || 0) - valid.length
        });

        if (valid.length === 0) {
            logger.error('No valid configs—exiting.');
            performanceMonitor.complete({ status: 'failed', reason: 'No valid configurations' });
            return;
        }

        performanceMonitor.startStep('Overall CSV processing', { validAgentCount: valid.length });
        const processingResults = await processCSVFiles(valid, performanceMonitor);
        performanceMonitor.endStep('Overall CSV processing', processingResults);

        const finalMetrics = performanceMonitor.complete({
            status: 'success',
            totalAgentsProcessed: valid.length,
            ...processingResults
        });
        logger.info('Job completed successfully.', { performanceMetrics: finalMetrics.summary });
    } catch (error) {
        logger.error('Error in inbound agent job:', error);
        performanceMonitor.complete({
            status: 'error',
            error: error.message,
            stack: error.stack
        });
        throw error;
    }
}

/**
 * Schedule cron jobs for inbound agents using cached cron configurations
 */
async function scheduleCronJobsForInbound() {
    try {
        // Check if cron jobs are already scheduled to avoid duplicates
        if (global.cronJobsScheduled) {
            logger.info('Cron jobs already scheduled, skipping...');
            return;
        }

        const cronJobs = await getCachedCronConfigs();

        if (cronJobs.length === 0) {
            logger.warn('No active cron configurations found');
            return;
        }

        cronJobs.forEach((cronJob) => {
            cron.schedule(cronJob.schedule, async () => {
                logger.info(`Executing scheduled job: ${cronJob.name}`);
                try {
                    // Execute the inbound handler for the scheduled job
                    await inboundAgentHandler(cronJob);
                } catch (error) {
                    logger.error(`Error executing scheduled job ${cronJob.name}:`, error);
                }
            });
            logger.info(`Scheduled cron job: ${cronJob.name} with schedule: ${cronJob.schedule}`);
        });

        // Mark cron jobs as scheduled to prevent duplicates
        global.cronJobsScheduled = true;
        logger.info(`Successfully scheduled ${cronJobs.length} cron jobs for inbound agents`);
    } catch (error) {
        logger.error('Error scheduling cron jobs:', error);
    }
}

module.exports = { inboundAgentHandler, scheduleCronJobsForInbound };
